-- 修改图纸的CAD文件是否是必填
UPDATE `atour-con`.`t_template_group_expand` SET `not_cad` = 1  WHERE `file_header` IN ('公区百货家具深化方案', '公区材料列表', '公区固定家具深化方案', '公区活动家具深化方案', '公区弱电设备清单','厨房设备清单','客房房号方案','公区其他深化方案','客房其他深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区装饰画（喷绘方案）','客房区非标材料清单','客房区弱电设备清单','公区装饰画（喷绘方案）','室内全套VI方案','室外店招深化方案','室外VI方案','客房效果方案','勘测报告','机电勘测报告','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','设计验收（本次新增）') ;
UPDATE `atour-con`.`t_template_group_expand` SET `not_cad` = NULL WHERE `file_header` NOT IN ('公区百货家具深化方案', '公区材料列表', '公区固定家具深化方案', '公区活动家具深化方案', '公区弱电设备清单','厨房设备清单','客房房号方案','公区其他深化方案','客房其他深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区装饰画（喷绘方案）','客房区非标材料清单','客房区弱电设备清单','公区装饰画（喷绘方案）','室内全套VI方案','室外店招深化方案','室外VI方案','客房效果方案','勘测报告','机电勘测报告','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','设计验收（本次新增）') ;

-- 添加图纸的PDF文件是否为必填字段
ALTER TABLE `atour-con`.`t_template_group_expand`
    ADD COLUMN `pdf_mandatory` int NULL COMMENT 'PDF文件是否可选不适用非必填（0可选不适用PDF为非必传，1不可选不适用PDF为必传）' AFTER `drawing_change_code`;

UPDATE `atour-con`.`t_template_group_expand` SET `pdf_mandatory` = 1 WHERE `file_header` IN ('公区弱电设备清单','公区固定家具深化方案','公区活动家具深化方案','公区材料列表','公区百货家具深化方案','厨房设备深化方案（平面图）','厨房设备清单','客房房号方案','公区热水热源深化方案','公区暖通深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区家具图纸','客房区装饰画（喷绘方案）','客房客控深化方案','客房区弱电深化方案','客房区弱电设备清单','客房区热水热源深化方案','客房区新风深化方案','公区装饰画（喷绘方案）','室内全套VI方案','室外门头深化方案','室外店招深化方案','客房区空调深化方案','室外VI方案','客房消防图纸','客房综合机电管线图','客房平面方案（PPT）','客房综合天花点位图（CAD/PDF)','客房给排⽔施⼯图（CAD/PDF）','客房弱电施工图（CAD/PDF）','客房电气施工图（CAD/PDF）','客房平面图（CAD/PDF）','平面隔墙尺寸图（CAD/PDF）','客房暖通施工图（CAD/PDF）','客房装饰施工图','消防竣工图（喷淋、报警系统、消火栓、排烟）','公区消防图纸','公区平⾯图（PDF/CAD）','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','消防点位对照表、平面图','室外施工图（PDF/CAD）','公区综合天花板（CAD/PDF）','公区给排水施工图（CAD/PDF）（含厨房）','公区弱电施工图（CAD/PDF）（含厨房）','公区暖通施工图（CAD/PDF）（含厨房）','公区电气施工图（CAD/PDF）（含厨房）','亚朵百货施工图纸','公区装饰施工图（CAD/PDF）') ;

-- 添加图纸的PDF文件是否为必填字段
ALTER TABLE `atour-con`.`t_project_group_expand`
    MODIFY COLUMN `not_cad` int NULL DEFAULT NULL COMMENT 'CAD文件是否必填（0必填，1非必填）' AFTER `relation_code`,
    ADD COLUMN `pdf_mandatory` int NULL DEFAULT NULL COMMENT 'PDF文件是否可选不适用非必填（0可选不适用PDF为非必传，1不可选不适用PDF为必传） ' AFTER `drawing_change_code`;

UPDATE `atour-con`.`t_project_group_expand` SET `not_cad` = 1  WHERE `file_header` IN ('公区百货家具深化方案', '公区材料列表', '公区固定家具深化方案', '公区活动家具深化方案', '公区弱电设备清单','厨房设备清单','客房房号方案','公区其他深化方案','客房其他深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区装饰画（喷绘方案）','客房区非标材料清单','客房区弱电设备清单','公区装饰画（喷绘方案）','室内全套VI方案','室外店招深化方案','室外VI方案','客房效果方案','勘测报告','机电勘测报告','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','设计验收（本次新增）') ;
UPDATE `atour-con`.`t_project_group_expand` SET `not_cad` = NULL WHERE `file_header` NOT IN ('公区百货家具深化方案', '公区材料列表', '公区固定家具深化方案', '公区活动家具深化方案', '公区弱电设备清单','厨房设备清单','客房房号方案','公区其他深化方案','客房其他深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区装饰画（喷绘方案）','客房区非标材料清单','客房区弱电设备清单','公区装饰画（喷绘方案）','室内全套VI方案','室外店招深化方案','室外VI方案','客房效果方案','勘测报告','机电勘测报告','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','设计验收（本次新增）') ;
UPDATE `atour-con`.`t_project_group_expand` SET `pdf_mandatory` = 1 WHERE `file_header` IN ('公区弱电设备清单','公区固定家具深化方案','公区活动家具深化方案','公区材料列表','公区百货家具深化方案','厨房设备深化方案（平面图）','厨房设备清单','客房房号方案','公区热水热源深化方案','公区暖通深化方案','客房非标家具选型方案','公区软装方案（PDF/PPT）','客房区家具图纸','客房区装饰画（喷绘方案）','客房客控深化方案','客房区弱电深化方案','客房区弱电设备清单','客房区热水热源深化方案','客房区新风深化方案','公区装饰画（喷绘方案）','室内全套VI方案','室外门头深化方案','室外店招深化方案','客房区空调深化方案','室外VI方案','客房消防图纸','客房综合机电管线图','客房平面方案（PPT）','客房综合天花点位图（CAD/PDF)','客房给排⽔施⼯图（CAD/PDF）','客房弱电施工图（CAD/PDF）','客房电气施工图（CAD/PDF）','客房平面图（CAD/PDF）','平面隔墙尺寸图（CAD/PDF）','客房暖通施工图（CAD/PDF）','客房装饰施工图','消防竣工图（喷淋、报警系统、消火栓、排烟）','公区消防图纸','公区平⾯图（PDF/CAD）','公区概念方案（PPT）','室外效果方案（PDF/PPT）','公区效果方案（PDF/PPT）','消防点位对照表、平面图','室外施工图（PDF/CAD）','公区综合天花板（CAD/PDF）','公区给排水施工图（CAD/PDF）（含厨房）','公区弱电施工图（CAD/PDF）（含厨房）','公区暖通施工图（CAD/PDF）（含厨房）','公区电气施工图（CAD/PDF）（含厨房）','亚朵百货施工图纸','公区装饰施工图（CAD/PDF）') ;

-- 验收单 添加验收文件字段
ALTER TABLE `atour-con`.`t_project_completion_receipt`
    ADD COLUMN `acceptance_document` varchar(255) NULL COMMENT '验收文件' AFTER `rectification_reply`;

-- 客房设计交底申请  改为  设计师 提交
UPDATE `atour-con`.`t_template_group` SET `role_code` = 'sjfzr' WHERE `node_name` LIKE '%客房设计交底申请%';
-- 客房设计交底申请  图纸客房设计交底要求，改为必填
UPDATE `atour-con`.`t_project_template` SET `end_sign` = 1 WHERE `node_code` LIKE '%des-00169%' AND `is_edit` LIKE '%1%' AND `node_code` IN ('des-00169020','des-00169021','des-00169022','des-00169023','des-00169024','des-00169025','des-00169026','des-00169027');


-- 删除空调深化审批第三审
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1739195577951981575;

-- 公区软装方案（PDF/PPT）关联设计图纸
UPDATE `atour-con`.`t_template_group_expand` SET `relation_code` = 'pad-0013103802' WHERE `expand_id` = 1736292287262101538;

-- 竣工验收  竣工验收整改可以查看项目图纸
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179845, 'engineering', NULL, '图纸资料', 'eng-00133070', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:34:08', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179847, 'engineering(newYDXJD)', NULL, '图纸资料', 'eng-00133070', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:34:08', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179846, 'engineering', NULL, '项目所有确认图纸同步', 'eng-00133071', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'skip_page', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:34:08', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179848, 'engineering(newYDXJD)', NULL, '项目所有确认图纸同步', 'eng-00133071', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'skip_page', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:34:08', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179850, 'engineering', NULL, '图纸资料', 'eng-00135035', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:50:05', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179852, 'engineering(newYDXJD)', NULL, '图纸资料', 'eng-00135035', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:50:05', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179851, 'engineering', NULL, '项目所有确认图纸同步', 'eng-00135036', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'skip_page', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:50:05', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179853, 'engineering(newYDXJD)', NULL, '项目所有确认图纸同步', 'eng-00135036', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'skip_page', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 15:50:05', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781123, 1843591114286179850, 'engineering', 1700404548427971200, '图纸资料', 'eng-00135035', 10, 3, b'1', b'0', '2025-02-07 15:53:05', '2025-02-07 15:53:05', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781124, 1843591114286179852, 'engineering(newYDXJD)', 1785230559673454592, '图纸资料', 'eng-00135035', 10, 3, b'1', b'0', '2025-02-07 15:53:05', '2025-02-07 15:53:05', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781125, 1843591114286179851, 'engineering', 1700404548427971200, '项目所有确认图纸同步', 'eng-00135036', 11, 3, b'1', b'0', '2025-02-07 15:53:05', '2025-02-07 15:53:05', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781126, 1843591114286179853, 'engineering(newYDXJD)', 1785230559673454592, '项目所有确认图纸同步', 'eng-00135036', 11, 3, b'1', b'0', '2025-02-07 15:53:05', '2025-02-07 15:53:05', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781119, 1843591114286179845, 'engineering', 1700404548427971078, '图纸资料', 'eng-00133070', 12, 3, b'1', b'0', '2025-02-07 15:41:13', '2025-02-07 15:41:21', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781121, 1843591114286179847, 'engineering(newYDXJD)', 1785230548105564160, '图纸资料', 'eng-00133070', 12, 3, b'1', b'0', '2025-02-07 15:42:55', '2025-02-07 15:43:12', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781120, 1843591114286179846, 'engineering', 1700404548427971078, '项目所有确认图纸同步', 'eng-00133071', 13, 3, b'1', b'0', '2025-02-07 15:41:10', '2025-02-07 15:41:19', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781122, 1843591114286179848, 'engineering(newYDXJD)', 1785230548105564160, '项目所有确认图纸同步', 'eng-00133071', 13, 3, b'1', b'0', '2025-02-07 15:42:51', '2025-02-07 15:43:08', '系统:0', NULL, NULL, NULL, NULL);

UPDATE `atour-con`.`t_template_queue` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';

UPDATE `atour-con`.`t_template_queue` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';

UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 14 WHERE `node_code` = 'eng-00133026';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 15 WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 16 WHERE `node_code` = 'eng-00133055';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 17 WHERE `node_code` = 'eng-00133065';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 18 WHERE `node_code` = 'eng-00133066';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 19 WHERE `node_code` = 'eng-00133067';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 20 WHERE `node_code` = 'eng-00133068';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 17 WHERE `node_code` = 'eng-00135031';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 18 WHERE `node_code` = 'eng-00135021';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 19 WHERE `node_code` = 'eng-00135022';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 20 WHERE `node_code` = 'eng-00135023';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 21 WHERE `node_code` = 'eng-00135024';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_index` = 22 WHERE `node_code` = 'eng-00135025';


INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_1';

INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_2';

INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_4';

INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_5';

INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_6';

INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_7';

INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_8';

INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00133070','eng-00133071')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_9';


INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_1';

INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_2';

INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_4';

INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_5';

INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_6';

INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_7';

INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_8';

INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135035','eng-00135036')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_9';

-- 竣工自检任务，添加业主/现长签字文件上传
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179854, 'engineering', NULL, '电子签', 'eng-00127340', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179855, 'engineering', NULL, '业主/现长签字', 'eng-00127341', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179856, 'engineering', NULL, '请上传业主/现长签字版文件', 'eng-00127342', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179857, 'engineering', NULL, '日期', 'eng-00127343', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179858, 'engineering(newYDXJD)', NULL, '电子签', 'eng-00127340', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179859, 'engineering(newYDXJD)', NULL, '业主/现长签字', 'eng-00127341', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179860, 'engineering(newYDXJD)', NULL, '请上传业主/现长签字版文件', 'eng-00127342', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179861, 'engineering(newYDXJD)', NULL, '日期', 'eng-00127343', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-02-07 10:31:25', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781127, 1843591114286179854, 'engineering', 1700404548427969580, '电子签', 'eng-00127340', 285, 3, b'1', b'0', '2025-02-07 17:48:38', '2025-02-07 17:48:38', '系统:0', NULL, NULL, NULL, 'master_template');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781131, 1843591114286179858, 'engineering(newYDXJD)', 1785230471727288320, '电子签', 'eng-00127340', 285, 3, b'1', b'0', '2025-02-07 17:50:16', '2025-02-07 17:50:16', '系统:0', NULL, NULL, NULL, 'split_plate');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781128, 1843591114286179855, 'engineering', 1700404548427969580, '业主/现长', 'eng-00127341', 286, 3, b'1', b'0', '2025-02-07 17:48:38', '2025-02-07 17:48:38', '系统:0', NULL, NULL, NULL, 'master_template');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781132, 1843591114286179859, 'engineering(newYDXJD)', 1785230471727288320, '业主/现长', 'eng-00127341', 286, 3, b'1', b'0', '2025-02-07 17:50:16', '2025-02-07 17:50:16', '系统:0', NULL, NULL, NULL, 'split_plate');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781129, 1843591114286179856, 'engineering', 1700404548427969580, '请上传业主/现长签字版文件', 'eng-00127342', 287, 3, b'1', b'0', '2025-02-07 17:48:38', '2025-02-07 17:48:38', '系统:0', NULL, NULL, NULL, 'master_template');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781133, 1843591114286179860, 'engineering(newYDXJD)', 1785230471727288320, '请上传业主/现长签字版文件', 'eng-00127342', 287, 3, b'1', b'0', '2025-02-07 17:50:16', '2025-02-07 17:50:16', '系统:0', NULL, NULL, NULL, 'split_plate');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781130, 1843591114286179857, 'engineering', 1700404548427969580, '日期', 'eng-00127343', 288, 3, b'1', b'0', '2025-02-07 17:48:38', '2025-02-07 17:48:38', '系统:0', NULL, NULL, NULL, 'master_template');
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781134, 1843591114286179861, 'engineering(newYDXJD)', 1785230471727288320, '日期', 'eng-00127343', 288, 3, b'1', b'0', '2025-02-07 17:50:16', '2025-02-07 17:50:16', '系统:0', NULL, NULL, NULL, 'split_plate');


INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_1';

INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_2';

INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_4';

INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_5';

INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_6';

INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_7';

INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_8';

INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00127340','eng-00127341','eng-00127342','eng-00127343','eng-00127340','eng-00127341','eng-00127342','eng-00127343')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工自检'
  AND b.node_table_name = 't_project_node_info_9';

-- 修改二级任务顺序
UPDATE `atour-con`.`t_template_group` SET `node_index` = 1 WHERE `template_group_id` = 1783808441786175497;
UPDATE `atour-con`.`t_template_group` SET `node_index` = 2 WHERE `template_group_id` = 1783808441786175498;
UPDATE `atour-con`.`t_template_group` SET `node_index` = 3 WHERE `template_group_id` = 1783808441786175499;
UPDATE `atour-con`.`t_template_group` SET `node_index` = 13 WHERE `template_group_id` = 1783808441786175500;
UPDATE `atour-con`.`t_template_group` SET `node_index` = 14 WHERE `template_group_id` = 1783808441786175502;
UPDATE `atour-con`.`t_template_group` SET `node_index` = 12 WHERE `template_group_id` = 1783808441786175501;

-- 完善公区设计交底申请任务，材料样品明细
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179839, 'public_area_design', NULL, '材料类别', 'pad-00145050', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, '公区材料小样', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179840, 'public_area_design(newYDXJD)', NULL, '材料类别', 'pad-00145050', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, '公区材料小样', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179841, 'public_area_design', NULL, '材料图片', 'pad-00145051', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179842, 'public_area_design(newYDXJD)', NULL, '材料图片', 'pad-00145051', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179843, 'public_area_design', NULL, '描述', 'pad-00145052', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286179844, 'public_area_design(newYDXJD)', NULL, '描述', 'pad-00145052', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2025-01-22 14:16:55', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781112, 1843591114286179839, 'public_area_design', 1736292276948308025, '材料类别', 'pad-00145050', 48, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781113, 1843591114286179840, 'public_area_design(newYDXJD)', 1785229616521285680, '材料类别', 'pad-00145050', 48, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781114, 1843591114286179841, 'public_area_design', 1736292276948308025, '材料图片', 'pad-00145051', 49, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781115, 1843591114286179842, 'public_area_design(newYDXJD)', 1785229616521285680, '材料图片', 'pad-00145051', 49, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781116, 1843591114286179843, 'public_area_design', 1736292276948308025, '描述', 'pad-00145052', 50, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987781117, 1843591114286179844, 'public_area_design(newYDXJD)', 1785229616521285680, '描述', 'pad-00145052', 50, 3, b'1', b'0', '2025-01-22 14:24:04', '2025-01-22 14:24:04', '系统:0', NULL, NULL, NULL, NULL);


INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_1';





INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_2';





INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_3';





INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_4';





INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_5';





INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_6';





INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_7';





INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_8';


INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('pad-00145050','pad-00145051','pad-00145052')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '公区设计交底申请'
  AND b.node_table_name = 't_project_node_info_9';

