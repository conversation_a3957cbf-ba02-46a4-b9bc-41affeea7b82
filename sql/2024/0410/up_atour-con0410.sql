UPDATE `atour-con`.`t_project_template` SET `node_name` = '预计设计启动时间' WHERE `template_id` = 1663214820075376718;

DELETE FROM `atour-con`.`t_planevent_nodecode_relation` WHERE `node_code` = 'des-001' AND `plan_event_id` = 2;

DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1737731124106498048;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_num` = '1.1' WHERE `approve_template_detail_id` = 1737732373270892544;

DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1737732124896792576;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1407639269599150107' WHERE `approve_template_detail_id` = 1722576239115505664;

/*
 Navicat Premium Data Transfer

 Source Server         : **********
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : **********:3306
 Source Schema         : atour-con

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 10/04/2024 14:12:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_plan_event_info
-- ----------------------------
DROP TABLE IF EXISTS `t_plan_event_info`;
CREATE TABLE `t_plan_event_info`  (
                                      `plan_event_id` bigint NOT NULL AUTO_INCREMENT COMMENT '计划事件主键',
                                      `plan_event_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划事件名称',
                                      `group_id` bigint NULL DEFAULT NULL COMMENT '所有模板的集合表t_template_collection的group_id外键',
                                      `plan_event_belong` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划时间所属(0:项目启动1：项目设计2：项目施工)',
                                      `plan_event_duration` smallint NULL DEFAULT NULL COMMENT '计划工期',
                                      `all_duration` smallint NULL DEFAULT NULL COMMENT '总工期',
                                      `del_flag` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标记（0：正常 2：删除）',
                                      `sort` smallint NULL DEFAULT NULL COMMENT '排序码',
                                      PRIMARY KEY (`plan_event_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '计划事件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_plan_event_info
-- ----------------------------
INSERT INTO `t_plan_event_info` VALUES (1, '筹建启动会', 1, '0', 7, 7, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (3, '设计启动', 1, '1', 0, 0, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (4, '客房平面确认', 1, '1', 11, 13, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (5, '样板间施工图确认', 1, '1', 20, 22, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (6, '客房交底', 1, '1', 5, 45, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (7, '样板间验收', 1, '1', 48, 72, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (8, '公区概念方案确认', 1, '1', 16, 18, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (9, '公区效果图方案确认', 1, '1', 18, 38, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (10, '公区施工图确认', 1, '1', 18, 58, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (11, '公区交底', 1, '1', 5, 65, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (12, '样板间施工图提交', 1, '1', 16, 18, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (13, '样板间施工图确认', 1, '1', 6, 24, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (14, '样板间验收', 1, '1', 50, 74, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (15, '公区概念确认', 1, '1', 18, 20, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (16, '公区效果图方案确认', 1, '1', 20, 40, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (17, '公区设计封样提交', 1, '1', 10, 50, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (18, '公区施工图确认', 1, '1', 10, 60, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (19, '公区交底', 1, '1', 7, 67, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (20, '设计修改通知单', 1, '1', 5, 72, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (21, '正式开工', 1, '2', 0, 0, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (22, '样板间隐蔽', 1, '2', 25, 25, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (23, '样板间验收', 1, '2', 25, 50, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (24, '隐蔽验收', 1, '2', 20, 70, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (25, '隐蔽验收确认', 1, '2', 10, 80, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (26, '家具进场', 1, '2', 20, 100, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (27, '形象进度80%', 1, '2', 20, 120, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (28, '竣工自检', 1, '2', 20, 140, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (37, '筹建', 1, '0', 1, 1, '0', NULL);
INSERT INTO `t_plan_event_info` VALUES (38, '客房施工图确认', 1, '1', 14, 38, '0', NULL);

SET FOREIGN_KEY_CHECKS = 1;
