package com.bassims.utils.reflex;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.bassims.utils.SpringUtil;
import com.bassims.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 反射调用工具类
 * <AUTHOR>
 * @Date 2022/10/21 0021
 */
@Slf4j
public class ReflexUtil {

    private static final List<String> WRAP_CLASS = Arrays.asList("short", "byte", "int", "long", "char", "boolean", "float", "double");

    /**
     * spring 反射调用类方法
     *
     * @param className
     * @param methodName
     */
    public static List<NodeFiledValueList> reflex(String className, String methodName, Boolean isRequestBody, Boolean responseIsList, String requestParam, String responseParam) throws Exception {
        // 创建返回数据List
        List<NodeFiledValueList> result = new ArrayList<>();
        //01 获取spring容器中的Bean
        //类名首字母小写
        className = StringUtils.uncapitalize(className);
        Object proxyObject = SpringUtil.getBean(className);
        //02 利用bean获取class对象，进而获取本类以及父类或者父接口中所有的公共方法(public修饰符修饰的)
        // 获取全部方法
        Method[] methods = proxyObject.getClass().getDeclaredMethods();
        // 当前接口
        Method curMethod = null;
        // 匹配当前接口
        if (methods.length > 0) {
            for (int i = 0; i < methods.length; i++) {
                if (methodName.equals(methods[i].getName())) {
                    curMethod = methods[i];
                }
            }
        }
        //03 获取参数
        List<NodeFiledValue> requestNodeFiledValueList = JSONObject.parseArray(requestParam, NodeFiledValue.class);
        Map<String, NodeFiledValue> requestNodeFiledValueMap = requestNodeFiledValueList.stream().collect(Collectors.toMap(NodeFiledValue::getField, e -> e));
        // 当前接口匹配成功
        if (curMethod != null) {
            // 获取参数列表
            Parameter[] parameters = curMethod.getParameters();
            // 参数对象数据
            Object[] paramObject = new Object[parameters.length];
            // 实体参数对象
            Object singleObject = new Object();
            if (parameters.length > 0) {
                for (int j = 0; j < parameters.length; j++) {
                    // 参数
                    Parameter parameter = parameters[j];
                    // 类型名称
                    String typeName = parameter.getType().getName();
                    // 参数编码
                    String paramCode = parameter.getName();

                    // 如果当前属于基本类型
                    if (WRAP_CLASS.contains(typeName)) {
                        // 根据参数编码获取值
                        NodeFiledValue nodeFiledValue = requestNodeFiledValueMap.get(paramCode);
                        // 不为空
                        if (nodeFiledValue != null) {
                            // 添加值到集合
                            paramObject[j] = castValueByType(typeName, String.valueOf(nodeFiledValue.getValue()));
                        }
                    } else {
                        // 创建对象
                        Object curParamObj = parameter.getType().newInstance();
                        // 获取全部的字段
                        Field[] fields = curParamObj.getClass().getDeclaredFields();
                        if (fields.length > 0) {
                            for (int m = 0; m < fields.length; m++) {
                                // 获取字段
                                Field field = fields[m];
                                // 获取字段名称
                                String fileCode = field.getName();
                                // 获取字段类型名称
                                String filedType = field.getType().getName();
                                // 根据字段名称获取值
                                NodeFiledValue nodeFiledValue = requestNodeFiledValueMap.get(fileCode);
                                // 不为空
                                if (nodeFiledValue != null) {
                                    // 设置值
                                    field.setAccessible(true);
                                    // 转换类型
                                    field.set(curParamObj, castValueByType(filedType, String.valueOf(nodeFiledValue.getValue())));
                                }
                            }
                        }
                        singleObject = curParamObj;
                    }
                }
            }
            // 创建返回对象
            Object returnObject;
            if (isRequestBody != null && isRequestBody) {
                returnObject = curMethod.invoke(proxyObject, singleObject);
                // 参数列表
            } else {
                returnObject = curMethod.invoke(proxyObject, paramObject);
            }
            List<NodeFiledValue> responseNodeFiledValueList = JSONObject.parseArray(responseParam, NodeFiledValue.class);
            if (responseIsList != null && responseIsList) {
                // 判断类型
                if (returnObject instanceof List) {
                    // 强制类型转换
                    List<Object> objectList = (List<Object>) returnObject;
                    // 判断不为空
                    if (CollUtil.isNotEmpty(objectList)) {
                        for (Object object : objectList) {
                            NodeFiledValueList nodeFiledValueList = new NodeFiledValueList();
                            nodeFiledValueList.setNodeFiledValueList(objectToNodeFiledValue(object, responseNodeFiledValueList));
                            // 添加到result中
                            result.add(nodeFiledValueList);
                        }
                    }
                }
                // 响应参数是对象
            } else {
                NodeFiledValueList nodeFiledValueList = new NodeFiledValueList();
                nodeFiledValueList.setNodeFiledValueList(objectToNodeFiledValue(returnObject, responseNodeFiledValueList));
                // 添加到result中
                result.add(nodeFiledValueList);
            }
        }
        return result;
    }


    /**
     * 根据类型转换值
     *
     * @param type   类型值
     * @param string 转换值
     * @return
     */
    public static Object castValueByType(String type, String string) {
        if ("java.lang.String".equals(type)) {
            return new String(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Integer".equals(type)) {
            return Integer.parseInt(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Double".equals(type)) {
            return Double.parseDouble(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Float".equals(type)) {
            return Float.parseFloat(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Long".equals(type)) {
            return Long.parseLong(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Short".equals(type)) {
            return Short.parseShort(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Byte".equals(type)) {
            return Byte.parseByte(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Boolean".equals(type)) {
            return Boolean.parseBoolean(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("java.lang.Character".equals(type)) {
            return (StringUtils.isEmpty(string) ? " " : string).charAt(0);
        } else if ("java.math.BigDecimal".equals(type)) {
            return new BigDecimal(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("int".equals(type)) {
            return Integer.parseInt(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("double".equals(type)) {
            return Double.parseDouble(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("float".equals(type)) {
            return Float.parseFloat(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("long".equals(type)) {
            return Long.parseLong(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("short".equals(type)) {
            return Short.parseShort(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("byte".equals(type)) {
            return Byte.parseByte(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("boolean".equals(type)) {
            return Boolean.parseBoolean(StringUtils.isEmpty(string) ? "0" : string);
        } else if ("char".equals(type)) {
            return (StringUtils.isEmpty(string) ? " " : string).charAt(0);
        }
        return null;
    }

    private static List<NodeFiledValue> objectToNodeFiledValue(Object obj, List<NodeFiledValue> nodeFiledValueList) throws Exception {
        Map<String, NodeFiledValue> nodeFiledValueListMap = nodeFiledValueList.stream().collect(Collectors.toMap(NodeFiledValue::getField, e -> e));
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            // 获取nodeCode
            NodeFiledValue nodeFiledValue = nodeFiledValueListMap.get(fieldName);
            if (nodeFiledValue == null) {
                continue;
            }
            // 获取字段值
            nodeFiledValue.setValue(field.get(obj));
        }
        return nodeFiledValueList;
    }


}
