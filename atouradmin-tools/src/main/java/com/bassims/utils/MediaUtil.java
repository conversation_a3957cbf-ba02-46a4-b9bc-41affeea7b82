package com.bassims.utils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Locale;

/**
 * @Title: MediaUtil
 * @ProjectName: atouradmin
 * @Description: TODO
 * @author: Administrator
 * @data: 2025/1/3 0003 19:14
 */
public class MediaUtil {

    private static final String HEIC_MAGIC_NUMBER = "ftypheic";

    public static boolean isHeicImage(InputStream inputStream) {
        try {
            byte[] headerBytes = new byte[20];
            inputStream.read(headerBytes);
            String headerString = new String(headerBytes, StandardCharsets.UTF_8).toLowerCase(Locale.ROOT);
            return headerString.contains(HEIC_MAGIC_NUMBER);
        } catch (Exception e) {
            // 处理异常情况，例如读取错误等
            return false;
        }
    }
}
