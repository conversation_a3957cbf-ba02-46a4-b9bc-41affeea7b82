package com.bassims;

import cn.hutool.http.HttpUtil;
import com.bassims.modules.security.service.UserDetailsServiceImpl;
import com.bassims.utils.wordutil.WordUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LoginCacheTest {

    @Resource(name = "userDetailsService")
    private UserDetailsServiceImpl userDetailsService;

    @Resource(name = "wordUtils")
    private WordUtils wordUtils;

    @Value("${store.path.maintenance}")
    private String maintenancePath;

    @Test
    public void testCache() throws Exception {
        /*long start1 = System.currentTimeMillis();
        int size = 10000;
        for (int i = 0; i < size; i++) {
            userDetailsService.loadUserByUsername("admin");
        }
        long end1 = System.currentTimeMillis();
        //关闭缓存
        userDetailsService.setEnableCache(false);
        long start2 = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            userDetailsService.loadUserByUsername("admin");
        }
        long end2 = System.currentTimeMillis();
        System.out.print("使用缓存：" + (end1 - start1) + "毫秒\n 不使用缓存：" + (end2 - start2) + "毫秒");*/
       /* String className = "SupplierPmServiceImpl";
        String methodName = "queryAllNoPage";
        String request = "[{\"field\":\"supplierId\",\"nodeCode\":\"con-00201\",\"value\":17}]";

        String response = "[{\"field\":\"supplierId\"},{\"field\":\"phone\"}]";
        List<NodeFiledValueList> reflex = ReflexUtil.reflex(className, methodName, true, true, request, response);
        System.out.println(reflex);*/
//        String path = new ClassPathResource("/template/template.ftl").getPath();
//        File file = ResourceUtils.getFile("classpath:templates/template.ftl");
//        System.out.println("===========================路径"+path);
//        File file = new File(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH).getPath());
//        System.out.println(file.getAbsolutePath());

        /*NoticeToProceed noticeToProceed = new NoticeToProceed();
        noticeToProceed.setCompany("南京八金木科技有限公司");
        noticeToProceed.setProjectName("测试word文档生成是否可行是否可行是否可行是否可行是否可行");
        noticeToProceed.setStartTime("2022年10月31日");
        noticeToProceed.setProjectAddress("南京八金木科技有限公司小小工位1号位");
        noticeToProceed.setProjectDetail("测试word文档生成是否可行是否可行是否可行是否可行是否可行，这不是在测试呢么");
        noticeToProceed.setEndTime("2022年10月31日");
        noticeToProceed.setProjectContent("1、放一个文档，测试 2、再生成一个文档，测试");
        noticeToProceed.setDesigner("小熊同学");
        noticeToProceed.setDesignerPhone("18013835427");
        noticeToProceed.setDesignBoss("小张同学");
        noticeToProceed.setDesignBossPhone("15203997213");
        noticeToProceed.setSendTime("2022年10月31日");
        noticeToProceed.setManagerCount("20");
        noticeToProceed.setPm("小徐同学、小谭同学、小熊同学");
        noticeToProceed.setManagers("小张同学、小徐同学、小谭同学、小熊同学");
        noticeToProceed.setConstructionerCount("10");
        noticeToProceed.setReturnTime("2022年10月31日");
        String path = wordUtils.createDoc("noticeToProceed.ftl", noticeToProceed);*/

        /*Contract contract = new Contract();
        contract.setCompany("南京八金木科技有限公司");
        contract.setProjectName("测试word文档生成是否可行是否可行是否可行是否可行是否可行");
        contract.setProjectAddress("南京八金木科技有限公司小小工位1号位");
        contract.setSendTime("2022年10月31日");
        contract.setReceiveTime("2022年10月31日");
        contract.setLegalPerson("");
        contract.setPm("");
        contract.setReturnTime("");
        String path = wordUtils.createDoc("contract.ftl", contract);
        File file = new File(path);

        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();

        FileUtil.downloadFile(request, response, file, false);*/


        Map<String,Object> map = new HashMap<>();
        map.put("pageNumber",1);
        map.put("pageSize",10);
        map.put("updateTimeBegin",1485851097000L);
        map.put("updateTimeEnd",1675153497000L);
        String post = HttpUtil.post(maintenancePath, map);
        System.out.println(post);

    }


}
