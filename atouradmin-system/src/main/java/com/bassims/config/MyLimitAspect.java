/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.annotation.MyDataPermission;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectStakeholders;
import com.bassims.modules.atour.repository.ProjectApproveRepository;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atour.repository.SysUsersCityRepository;
import com.bassims.modules.atour.repository.SysUsersRolesRepository;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersQueryCriteria;
import com.bassims.modules.atour.service.dto.TemplateCustomSignageQueryCriteria;
import com.bassims.modules.system.domain.Role;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Aspect
@Component
public class MyLimitAspect {
    @Autowired
    private SysUsersCityRepository sysUsersCityRepository;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private SysUsersRolesRepository sysUsersRolesRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Pointcut("@annotation(com.bassims.annotation.MyDataPermission)")
    public void pointcut() {
    }
    @Before("pointcut()")
    public void doBefore(JoinPoint point){
//        try{
            Long userId = SecurityUtils.getCurrentUserId();
            MethodSignature methodSignature = (MethodSignature)point.getSignature();
            MyDataPermission permission = methodSignature.getMethod().getAnnotation(MyDataPermission.class);
            String title = permission.title();
            if(userId!=null&&userId>1){
                if(StringUtils.isNotEmpty(title)){
                    if(sysUsersCityRepository.queryUserLimit(userId,title)<1){
                        throw new BadRequestException("你没有访问权限");
                    }
                }else{
                    Object[] args = point.getArgs();
                    if(args.length>0){
                        Long projectId = 0l;
                        if(args[0] instanceof String){
                            projectId = Long.parseLong(args[0].toString());
                        }else if(args[0] instanceof Long){
                            projectId = (Long)args[0];
                        }else if(args[0] instanceof TemplateCustomSignageQueryCriteria){
                            TemplateCustomSignageQueryCriteria criteria = (TemplateCustomSignageQueryCriteria)args[0];
                            projectId = criteria.getProjectId();
                        }else if(args[0] instanceof ProjectStakeholdersQueryCriteria){
                            ProjectStakeholdersQueryCriteria criteria = (ProjectStakeholdersQueryCriteria)args[0];
                            projectId = criteria.getProjectId();
                        }
                        if(projectId!=null&&projectId>0){
                            Long submeterProjectId = projectNodeInfoService.getSubmeterProjectId(projectId);
                            //查询项目在项干系人
                            LambdaQueryWrapper<ProjectStakeholders> projectStakeholdersWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                                    .eq(ProjectStakeholders::getProjectId, submeterProjectId)
                                    .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                                    .eq(ProjectStakeholders::getUserId,userId);
                            List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(projectStakeholdersWrapper);
                            //先看看是不是项目干系人
                            if(projectStakeholders==null||projectStakeholders.size()<1){
                                //再看看是不是有关于项目审批
                                if(projectApproveRepository.getCountByUser(userId,projectId)<1){
                                    //最后看看是不是超级管理员账户或业务管理员
                                    Set<Role>  roleSet = sysUsersRolesRepository.findByUserId(userId);
                                    boolean find = false;
                                    for(Role role:roleSet){
                                        if(role.getId()==1l || JhSystemEnum.RoleCodeEnum.YWGLY.getKey().equals(role.getRoleCode())){
                                            find = true;
                                        }
                                    }
                                    if(!find){
                                        throw new BadRequestException("你没有该项目的访问权限");
                                    }
                                }
                            }
                        }
                    }
                }
            }else if(userId<0){
                throw new BadRequestException("你没有访问权限");
            }
//        }catch (Exception e){
//            e.printStackTrace();
//            throw new BadRequestException("你没有访问权限");
//        }
    }
}
