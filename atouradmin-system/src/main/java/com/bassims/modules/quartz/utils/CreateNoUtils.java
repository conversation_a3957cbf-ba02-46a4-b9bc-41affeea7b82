package com.bassims.modules.quartz.utils;

import com.bassims.utils.RedisUtils;
import com.bassims.utils.SpringContextHolder;

/**
 * @program: rdp-bsd-parent
 * @author: cmy
 * @create: 2021-07-07 13:50
 **/
public class CreateNoUtils {
    /**
     * 消息单号规则
     *
     * @param prefixKey
     * @return
     */
    public static String createNo(String prefixKey) {
        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);
        Object no = redisUtils.get(prefixKey);
        int sort = 1000001;
        if (null != no) {
            sort = (Integer) no + 1;
        }
        redisUtils.set(prefixKey, sort);
        return prefixKey + sort;
    }
}
