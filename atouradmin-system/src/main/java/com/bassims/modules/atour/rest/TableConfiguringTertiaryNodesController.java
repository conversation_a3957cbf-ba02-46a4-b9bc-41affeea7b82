/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TableConfiguringTertiaryNodes;
import com.bassims.modules.atour.service.TableConfiguringTertiaryNodesService;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesDto;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "动态表格三级元素表管理")
@RequestMapping("/api/tableConfiguringTertiaryNodes")
public class TableConfiguringTertiaryNodesController {

    private static final Logger logger = LoggerFactory.getLogger(TableConfiguringTertiaryNodesController.class);

    private final TableConfiguringTertiaryNodesService tableConfiguringTertiaryNodesService;


    /**
    * @real_return {@link ResponseEntity<List<TableConfiguringTertiaryNodesDto>>}
    */
    @GetMapping("/list")
    @Log("查询动态表格三级元素表")
    @ApiOperation("查询动态表格三级元素表")
    public ResponseEntity<Object> query(TableConfiguringTertiaryNodesQueryCriteria criteria){
        return new ResponseEntity<>(tableConfiguringTertiaryNodesService.queryAll(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TableConfiguringTertiaryNodesDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询动态表格三级元素表")
    @ApiOperation("查询动态表格三级元素表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(tableConfiguringTertiaryNodesService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增动态表格三级元素表")
    @ApiOperation("新增动态表格三级元素表")
    public ResponseEntity<Object> create(@Validated @RequestBody TableConfiguringTertiaryNodes resources){
        return new ResponseEntity<>(tableConfiguringTertiaryNodesService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改动态表格三级元素表")
    @ApiOperation("修改动态表格三级元素表")
    public ResponseEntity<Object> update(@Validated @RequestBody TableConfiguringTertiaryNodes resources){
        tableConfiguringTertiaryNodesService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除动态表格三级元素表")
    @ApiOperation("删除动态表格三级元素表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        tableConfiguringTertiaryNodesService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


}