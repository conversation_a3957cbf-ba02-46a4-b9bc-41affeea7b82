/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectSpecialCaseDescription;
import com.bassims.modules.atour.service.ProjectSpecialCaseDescriptionService;
import com.bassims.modules.atour.service.dto.ProjectSpecialCaseDescriptionDto;
import com.bassims.modules.atour.service.dto.ProjectSpecialCaseDescriptionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-07
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目特殊情况说明表管理")
@RequestMapping("/api/projectSpecialCaseDescription")
public class ProjectSpecialCaseDescriptionController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSpecialCaseDescriptionController.class);

    private final ProjectSpecialCaseDescriptionService projectSpecialCaseDescriptionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectSpecialCaseDescriptionQueryCriteria criteria) throws IOException {
        projectSpecialCaseDescriptionService.download(projectSpecialCaseDescriptionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProjectSpecialCaseDescriptionDto>>}
    */
    @GetMapping("/list")
    @Log("查询项目特殊情况说明表")
    @ApiOperation("查询项目特殊情况说明表")
    public ResponseEntity<Object> query(ProjectSpecialCaseDescriptionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectSpecialCaseDescriptionService.queryAll(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProjectSpecialCaseDescriptionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询项目特殊情况说明表")
    @ApiOperation("查询项目特殊情况说明表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectSpecialCaseDescriptionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目特殊情况说明表")
    @ApiOperation("新增项目特殊情况说明表")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectSpecialCaseDescription resources){
        return new ResponseEntity<>(projectSpecialCaseDescriptionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目特殊情况说明表")
    @ApiOperation("修改项目特殊情况说明表")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectSpecialCaseDescription resources){
        projectSpecialCaseDescriptionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目特殊情况说明表")
    @ApiOperation("删除项目特殊情况说明表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectSpecialCaseDescriptionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}