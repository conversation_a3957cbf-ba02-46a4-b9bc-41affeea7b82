package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.service.ProjectTimeReportService;
import com.bassims.modules.atour.service.dto.ProjectTimeReportQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 项目时间报告控制器
 *
 * <AUTHOR>
 * @date 2022/07/09
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "项目时间报告管理")
@RequestMapping("/api/projectTimeReport")
public class ProjectTimeReportController {

    @Autowired
    private ProjectTimeReportService projectTimeReportService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectTimeReportQueryCriteria criteria) throws IOException {
        projectTimeReportService.download(response, criteria);
    }

    @Log("查询数据")
    @ApiOperation("查询数据")
    @GetMapping(value = "/queryTime")
    public ResponseEntity<Object> queryTime(ProjectTimeReportQueryCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(projectTimeReportService.queryTime(criteria, pageable));
    }

}
