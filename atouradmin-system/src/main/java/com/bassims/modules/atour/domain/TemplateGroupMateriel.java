/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2023-10-23
 **/
@Data
@TableName(value = "t_template_group_materiel")
public class TemplateGroupMateriel implements Serializable {

    @TableId(value = "template_group_id")
    @ApiModelProperty(value = "物料模版ID")
    private Long templateGroupId;

    @TableField(value = "group_name")
    @ApiModelProperty(value = "模版名称")
    private String groupName;

    @TableField(value = "group_code")
    @ApiModelProperty(value = "模版code")
    private String groupCode;

    @TableField(value = "materiel_no")
    @ApiModelProperty(value = "基础物料编码")
    private String materielNo;

    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型（码值project_type）")
    private String projectType;

    @TableField(value = "brand")
    @ApiModelProperty(value = "所属品牌")
    private String brand;

    @TableField(value = "store_type")
    @ApiModelProperty(value = "门店类型")
    private String storeType;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "create_dept")
    @ApiModelProperty(value = "创建部门")
    private Long createDept;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否已删除：0 正常，1 已删除")
    private Integer isDelete;

    @TableField(value = "tenant_id")
    @ApiModelProperty(value = "租户编号")
    private Long tenantId;

    @TableField(value = "is_must_mined")
    @ApiModelProperty(value = "是否必采（0必采，1非必采）")
    private Integer isMustMined;

    public void copy(TemplateGroupMateriel source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}