/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.TemplateTableRelation;
import com.bassims.modules.atour.repository.TemplateTableRelationRepository;
import com.bassims.modules.atour.service.TemplateTableRelationService;
import com.bassims.modules.atour.service.dto.TemplateTableRelationDto;
import com.bassims.modules.atour.service.dto.TemplateTableRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateTableRelationMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-12
 **/
@Service
public class TemplateTableRelationServiceImpl extends BaseServiceImpl<TemplateTableRelationRepository, TemplateTableRelation> implements TemplateTableRelationService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableRelationServiceImpl.class);

    @Autowired
    private TemplateTableRelationRepository templateTableRelationRepository;
    @Autowired
    private TemplateTableRelationMapper templateTableRelationMapper;

    @Override
    public Map<String, Object> queryAll(TemplateTableRelationQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<TemplateTableRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateTableRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateTableRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateTableRelationDto> queryAll(TemplateTableRelationQueryCriteria criteria) {
        return templateTableRelationMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateTableRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableRelationDto findById(Long tableRelationId) {
        TemplateTableRelation templateTableRelation = Optional.ofNullable(getById(tableRelationId)).orElseGet(TemplateTableRelation::new);
        ValidationUtil.isNull(templateTableRelation.getTableRelationId(), getEntityClass().getSimpleName(), "tableRelationId", tableRelationId);
        return templateTableRelationMapper.toDto(templateTableRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableRelationDto create(TemplateTableRelation resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTableRelationId(snowflake.nextId());
        save(resources);
        return findById(resources.getTableRelationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateTableRelation resources) {
        TemplateTableRelation templateTableRelation = Optional.ofNullable(getById(resources.getTableRelationId())).orElseGet(TemplateTableRelation::new);
        ValidationUtil.isNull(templateTableRelation.getTableRelationId(), "TemplateTableRelation", "id", resources.getTableRelationId());
        templateTableRelation.copy(resources);
        updateById(templateTableRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long tableRelationId : ids) {
            templateTableRelationRepository.deleteById(tableRelationId);
        }
    }

    @Override
    public void download(List<TemplateTableRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateTableRelationDto templateTableRelation : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("模版表格分组id", templateTableRelation.getTemplateTableGroupId());
            map.put("二级节点编码", templateTableRelation.getTwoNodeCode());
            map.put("三级节点编码", templateTableRelation.getThreeNodeCode());
            map.put("品牌code", templateTableRelation.getBrandCode());
            map.put("产品code", templateTableRelation.getProductCode());
            map.put(" createTime", templateTableRelation.getCreateTime());
            map.put(" updateTime", templateTableRelation.getUpdateTime());
            map.put(" createBy", templateTableRelation.getCreateBy());
            map.put(" updateBy", templateTableRelation.getUpdateBy());
            map.put("是否可用", templateTableRelation.getIsEnabled());
            map.put("是否删除", templateTableRelation.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public TemplateTableRelationDto getTemplateTable(String brandCode, String productCode, String nodeCode) {
        LambdaQueryWrapper<TemplateTableRelation> wrapper = Wrappers.lambdaQuery(TemplateTableRelation.class);
        wrapper.eq(TemplateTableRelation::getThreeNodeCode, nodeCode)
                .eq(TemplateTableRelation::getIsDelete, 0)
                .last("limit 1");
//                .eq(TemplateTableRelation::getBrandCode, brandCode)
//                .eq(TemplateTableRelation::getProductCode, productCode)
        TemplateTableRelation tableRelations = templateTableRelationRepository.selectOne(wrapper);
        TemplateTableRelationDto relationDto = templateTableRelationMapper.toDto(tableRelations);
        return relationDto;
    }

    @Override
    public List<TemplateTableRelationDto> getTemplateTableList(String brandCode, String productCode, Long parentId) {
        LambdaQueryWrapper<TemplateTableRelation> wrapper = Wrappers.lambdaQuery(TemplateTableRelation.class);
        wrapper.eq(TemplateTableRelation::getParentId, parentId)
                .eq(TemplateTableRelation::getIsDelete, 0);
//                .last("limit 1")
//                .eq(TemplateTableRelation::getBrandCode, brandCode)
//                .eq(TemplateTableRelation::getProductCode, productCode)
        List<TemplateTableRelation> tableRelations = templateTableRelationRepository.selectList(wrapper);
        List<TemplateTableRelationDto> relationDto = templateTableRelationMapper.toDto(tableRelations);
        return relationDto;
    }
}