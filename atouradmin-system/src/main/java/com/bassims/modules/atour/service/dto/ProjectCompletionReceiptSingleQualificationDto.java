/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-01-26
 **/
@Data
public class ProjectCompletionReceiptSingleQualificationDto implements Serializable {


    @ApiModelProperty(value = "联动测试/消防联动")
    String linkageTesting;

    @ApiModelProperty(value = "消防广播")
    String fireBroadcasting;

    @ApiModelProperty(value = "消防水系统")
    String fireWaterSystem;

    @ApiModelProperty(value = "门禁")
    String accessControl;

    @ApiModelProperty(value = "安装标准")
    String installationStandards;

    @ApiModelProperty(value = "消防疏散")
    String evacuate;



    @ApiModelProperty(value = "验收单内容名称")
    String countLabel;

    @ApiModelProperty(value = "验收单内容")
    String count;

    @ApiModelProperty(value = "验收状态")
    String acceptance;

}