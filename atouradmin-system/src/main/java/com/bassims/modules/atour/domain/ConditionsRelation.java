package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 *找关联的立项条件表
 */
@Data
@TableName(value="t_conditions_relation")
public class ConditionsRelation {

    @TableId(value = "conditions_relation_id")
    @ApiModelProperty(value = "主键id")
    private Long conditionsRelationId;

    @TableField(value = "condition_type_code")
    @ApiModelProperty(value = "条件类型code")
    private String conditionTypeCode;

    @TableField(value = "condition_type_group_name")
    @ApiModelProperty(value = "条件类型组名")
    private String conditionTypeGroupName;

    @TableField(value = "condition_code")
    @ApiModelProperty(value = "条件code")
    private String conditionCode;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;


    @TableField(value = "condition_type")
    @ApiModelProperty(value = "类型")
    private String conditionType;



    public void copy(ConditionsRelation source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
