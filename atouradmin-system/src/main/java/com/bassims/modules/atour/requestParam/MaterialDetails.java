package com.bassims.modules.atour.requestParam;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 材料细节
 *
 * <AUTHOR>
 *
 */
@Data
public class MaterialDetails {
    /**
     * 物资编码
     */
    private String materialCode;
    /**
     * 采购数量
     */
    private String perchaseQuantity;
    /**
     * 期望收货日期
     */
    private String exceptedReceivingDate;
    /**
     * 含税价格（小数点后两位）
     */
    private BigDecimal priceIncludingTax;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     *金额（物资总计金额）
     */
    private BigDecimal moneyAmount;

}
