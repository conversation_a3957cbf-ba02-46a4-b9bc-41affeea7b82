package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *  战区
 */
@Data
@TableName(value="s_war_zone")
@Accessors(chain = true)
public class WarZone {
    @TableId(value = "war_zone_id")
    @ApiModelProperty(value = "战区id")
    private Long warZoneId;

    @TableField(value = "brand_id")
    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @TableField(value = "war_zone_name")
    @ApiModelProperty(value = "战区名称")
    private String warZoneName;

    @TableField(value = "city_company")
    @ApiModelProperty(value = "战区value")
    private String cityCompany;
}
