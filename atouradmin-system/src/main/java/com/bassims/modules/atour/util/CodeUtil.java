package com.bassims.modules.atour.util;

import cn.hutool.core.date.DateTime;

import java.util.UUID;

import cn.hutool.core.util.ObjectUtil;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.service.dto.ProjectInfoTableCheckReceiptDTO;
import com.google.gson.Gson; // 导入Gson库，用于解析JSON
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


/**
 * <AUTHOR>
import com.google.gson.Gson; // 导入Gson库，用于解析JSON
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

 * @version 1.0
 * @description: TODO
 * @date 2023/10/25 13:37
 */
public class CodeUtil {

    public static String idGenerate1(String prefix,Long code) {
        code++;
        return prefix + new DateTime().toString("yyMMdd") + String.format("%04d", code);
    }

    public static int generateUniqueId() {
        UUID idOne = UUID.randomUUID();
        String str=""+idOne;
        int uid=str.hashCode();
        String filterStr=""+uid;
        str=filterStr.replaceAll("-", "");
        return Integer.parseInt(str);
    }


    public static void main(String[] args) {
//        Gson gson = new Gson(); // 创建Gson对象
//        JsonArray jsonArray = gson.fromJson("[\n" +
//                "  {\n" +
//                "    \"value\": \"inconsistent,increase,reduce,change\",\n" +
//                "    \"nodeCode\": \"eng-00103004,eng-00103007,eng-00103010,eng-00103013,eng-00103016,eng-00103019,eng-00103022,eng-00103025,eng-00103029,eng-00103032,eng-00103120,eng-00103122,eng-00103124,eng-00103126,eng-00103129,eng-00103131\"\n" +
//                "  }\n" +
//                "]", JsonArray.class); // 解析JSON字符串为JsonArray对象
//        for (int i = 0; i < jsonArray.size(); i++) {
//            JsonObject jsonObject = jsonArray.get(i).getAsJsonObject(); // 获取JsonArray中的JsonObject对象
//            JsonElement value = jsonObject.get("value");
//            JsonElement nodeCode = jsonObject.get("nodeCode");
//            System.out.println(value);
//            System.out.println(nodeCode);
////                boolean equals = jsonObject.get("tertiaryKey").getAsString().equals("nodeCode");
////                if (jsonObject.get("tertiaryKey").getAsString().equals("nodeCode")) {
////                    //获取nodeCode的值，逗号隔开 查询当前节点的值和 jsonObject.get("tertiaryKey").getAsString().equals("value") 相同,存在就说名，当前
////                    LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
////                            .eq(ProjectNodeInfo::getProjectId, nodeInfo.getProjectId())
////                            .in(ProjectNodeInfo::getRemark, jsonObject.get("tertiaryKey").getAsString().equals("value"))
////                            .in(ProjectNodeInfo::getNodeCode, jsonObject.get("tertiaryKey").getAsString().split(","));
////                    Long aLong = projectNodeInfoRepository.selectCount(wrapper);
////                    if (aLong > 0) {
////                        isMeetTheConditions = Boolean.FALSE;
////                    }
////                }
//        }
    }


//        String jsonString = "[{\"tertiaryKey\":\"total_item\",\"tertiaryType\":\"select\",\"tertiaryValue\":\"public_areas\",\"startSign\":\"completion_total_items\"},{\"tertiaryKey\":\"sub_item\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"外立面\",\"startSign\":\"\"},{\"tertiaryKey\":\"project\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"外围设施\",\"startSign\":\"\"},{\"tertiaryKey\":\"content\",\"tertiaryType\":\"select\",\"tertiaryValue\":\"exterior_wall\",\"startSign\":\"completion_content\"},{\"tertiaryKey\":\"sub_item_content\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"雨棚符合设计要求，安装牢固；悬挑超过2米，出具结构计算书；\",\"startSign\":\"\"},{\"tertiaryKey\":\"score\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"0\",\"startSign\":\"\"},{\"tertiaryKey\":\"standard_score\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"acceptance\",\"tertiaryType\":\"select\",\"tertiaryValue\":\"\",\"startSign\":\"acceptance\"},{\"tertiaryKey\":\"get_score\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"classification\",\"tertiaryType\":\"select\",\"tertiaryValue\":\"「必备项」\",\"startSign\":\"classification\"},{\"tertiaryKey\":\"qualification_rate\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"general_contracting_or_not\",\"tertiaryType\":\"select\",\"tertiaryValue\":\"\",\"startSign\":\"general_contracting_or_not\"},{\"tertiaryKey\":\"general_contracting_standard_score\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"overall_contracting_score\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"overall_contracting_score_rate\",\"tertiaryType\":\"input\",\"tertiaryValue\":\"\",\"startSign\":\"\"},{\"tertiaryKey\":\"check_attachments\",\"tertiaryType\":\"upload\",\"tertiaryValue\":\"\",\"startSign\":\"\"}]";
//
//        Gson gson = new Gson(); // 创建Gson对象
//        JsonArray jsonArray = gson.fromJson(jsonString, JsonArray.class); // 解析JSON字符串为JsonArray对象
//        for (int i = 0; i < jsonArray.size(); i++) {
//            JsonObject jsonObject = jsonArray.get(i).getAsJsonObject(); // 获取JsonArray中的JsonObject对象
//
//            if (jsonObject.get("tertiaryKey").getAsString().equals(JhSystemEnum.elementHeaderEnum.ACCEPTANCE.getKey())) {
//                //验收
//                jsonObject.addProperty("tertiaryValue", 88888); // 将tertiaryValue设置为新的值
//            }
//            if (jsonObject.get("tertiaryKey").getAsString().equals(JhSystemEnum.elementHeaderEnum.SCORE.getKey())) {
//                jsonObject.addProperty("tertiaryValue", 88888); // 将tertiaryValue设置为新的值
//            }
//            if (jsonObject.get("tertiaryKey").getAsString().equals(JhSystemEnum.elementHeaderEnum.SCORE.getKey())) {
//                jsonObject.addProperty("tertiaryValue", 88888); // 将tertiaryValue设置为新的值
//            }
//            if (jsonObject.get("tertiaryKey").getAsString().equals(JhSystemEnum.elementHeaderEnum.SCORE.getKey())) {
//                jsonObject.addProperty("tertiaryValue", 88888); // 将tertiaryValue设置为新的值
//            }
//        }
//        System.out.println(gson.toJson(jsonArray)); // 打印修改后的JsonArray对象
}
