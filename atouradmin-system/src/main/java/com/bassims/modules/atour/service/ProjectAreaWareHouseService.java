package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectAreaWareHouse;
import com.bassims.modules.atour.service.dto.ProjectAreaWareHouseQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 仓库面积
 */
public interface ProjectAreaWareHouseService extends BaseService<ProjectAreaWareHouse> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectAreaWareHouseQueryCriteria criteria) throws IOException;

    /**
     * 查询明细
     *
     * @param criteria 标准
     * @param pageable
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(ProjectAreaWareHouseQueryCriteria criteria, Pageable pageable);
}
