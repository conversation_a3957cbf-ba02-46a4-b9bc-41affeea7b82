/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateScheduleRelation;
import com.bassims.modules.atour.service.TemplateScheduleRelationService;
import com.bassims.modules.atour.service.dto.TemplateScheduleRelationQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-04-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_template_schedule_relation管理")
@RequestMapping("/api/templateScheduleRelation")
public class TemplateScheduleRelationController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateScheduleRelationController.class);

    private final TemplateScheduleRelationService templateScheduleRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateScheduleRelationQueryCriteria criteria) throws IOException {
        templateScheduleRelationService.download(templateScheduleRelationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<TemplateScheduleRelationDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_template_schedule_relation")
    @ApiOperation("查询t_template_schedule_relation")
    public ResponseEntity<Object> query(TemplateScheduleRelationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateScheduleRelationService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <TemplateScheduleRelationDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_template_schedule_relation")
    @ApiOperation("查询t_template_schedule_relation")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateScheduleRelationService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_template_schedule_relation")
    @ApiOperation("新增t_template_schedule_relation")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateScheduleRelation resources){
        return new ResponseEntity<>(templateScheduleRelationService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_template_schedule_relation")
    @ApiOperation("修改t_template_schedule_relation")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateScheduleRelation resources){
        templateScheduleRelationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_template_schedule_relation")
    @ApiOperation("删除t_template_schedule_relation")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateScheduleRelationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}