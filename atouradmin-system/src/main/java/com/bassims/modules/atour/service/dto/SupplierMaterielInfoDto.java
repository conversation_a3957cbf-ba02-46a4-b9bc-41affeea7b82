/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-09-14
**/
@Data
public class SupplierMaterielInfoDto implements Serializable {

    /** 主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private Long supContractId;

    private Long supplierId;

    private Long materielId;

    /** 物料名称 */
    private String materielName;

    /** 编号 */
    private String materielCode;

    /** 资产编号 */
    private String financeCode;

    /** 产品名称 */
    private String productName;

    /** 产品编号 */
    private String productCode;

    /** 物料种类 */
    private String materielClass;

    private Long sku;

    private BigDecimal salePrice;

    private BigDecimal buyPrice;

    private String skuAttr;

    private String supplyCycle;

    /** 创建人 */
    private Long createUser;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private Long updateUser;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 是否删除 */
    private Integer isDelete;
}