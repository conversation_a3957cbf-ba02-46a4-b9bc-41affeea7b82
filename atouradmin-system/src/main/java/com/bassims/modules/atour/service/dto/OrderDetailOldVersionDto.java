/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-04-01
**/
@Data
public class OrderDetailOldVersionDto implements Serializable {

    /** 订单详情主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long detailId;

    /** 订单主键 */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 物料表主键 */
    private Long materielId;

    /** 资产编号 */
    private String financeCode;

    /** 产品名称 */
    private String productName;

    /** 产品编号 */
    private String productCode;

    /** 物料编号 */
    private String materielCode;

    /** 一级分类 */
    private String seniorClass;

    /** 二级分类 */
    private String secondClass;

    /** 所属品牌 */
    private String brand;

    /** 规格 */
    private String spec;

    /** 型号 */
    private String model;

    /** 单位 */
    private String unit;

    /** 建议单价 */
    private BigDecimal suggestUnitPrice;

    /** 预约单库存 */
    private BigDecimal orderStock;

    /** 厂商id */
    private Long supplierId;

    /** 厂商中文名称 */
    private String supNameCn;

    /** 服务区数量 */
    private BigDecimal serviceNum;

    /** 玩具区数量 */
    private BigDecimal toyNum;

    /** 用品区数量 */
    private BigDecimal dailyNum;

    /** 快消区数量 */
    private BigDecimal fastNum;

    /** 纺织区数量 */
    private BigDecimal spinNum;

    /** 是否配件:1 配件 0不配件 */
    private Integer isMatch;

    /** 含税单价 */
    private BigDecimal unitPrice;

    /** 税率 */
    private BigDecimal rate;

    /** 合同编号 */
    private String contractNo;

    /** 到期时间 */
    private Timestamp endTime;

    /** 核算调整数量 */
    private BigDecimal accountAdjustNum;

    /** 审定数量 */
    private BigDecimal approvedNum;

    /** 小计 */
    private BigDecimal subtotal;

    /** 总计 */
    private BigDecimal totalPrice;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 排序 */
    private Integer sort;

    /** 备注 */
    private String remark;
}