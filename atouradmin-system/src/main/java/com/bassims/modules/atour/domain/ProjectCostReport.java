/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-22
**/
@Data
@TableName(value="project_cost_report")
public class ProjectCostReport implements Serializable {

    @TableField(value = "city_company_name")
    @ApiModelProperty(value = "分公司")
    private String cityCompanyName;

    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @TableField(value = "build_change")
    @ApiModelProperty(value = "施工变更金额")
    private BigDecimal buildChange;

    @TableField(value = "design_change")
    @ApiModelProperty(value = "设计变更金额")
    private BigDecimal designChange;

    @TableField(value = "miss_change")
    @ApiModelProperty(value = "漏项金额")
    private BigDecimal missChange;

    @TableField(value = "property_change")
    @ApiModelProperty(value = "物业特殊要求金额")
    private BigDecimal propertyChange;

    @TableField(value = "uncontroller_change")
    @ApiModelProperty(value = "不可控因素金额")
    private BigDecimal uncontrollerChange;

    @TableField(value = "predict_money")
    @ApiModelProperty(value = "金额")
    private BigDecimal predictMoney;

    @TableField(value = "report_submit_time")
    @ApiModelProperty(value = "提报时间")
    private String reportSubmitTime;

    @TableField(value = "report_due_time")
    @ApiModelProperty(value = "逾期时间")
    private BigDecimal reportDueTime;

    @TableField(value = "visa_submit_time")
    @ApiModelProperty(value = "提报时间")
    private String visaSubmitTime;

    @TableField(value = "visa_due_time")
    @ApiModelProperty(value = "逾期时间")
    private BigDecimal visaDueTime;

    public void copy(ProjectCostReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}