/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-11-06
**/
@Data
public class ProjectRoomQueryCriteria{

    @ApiModelProperty(value = "项目ID")
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "projectId")
    private Long projectId;

    @ApiModelProperty(value = "房间号")
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "roomNum")
    private String roomNum;

    @ApiModelProperty(value = "是否已经使用；1已经使用；0未使用")
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isUsed")
    private String isUsed;
}