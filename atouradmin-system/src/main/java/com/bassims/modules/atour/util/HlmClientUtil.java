package com.bassims.modules.atour.util;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Data
public class HlmClientUtil {

    @Value("${hlm-url.buildContacts}")
    private String buildContacts;
    @Value("${hlm-url.completionAcceptance}")
    private String completionAcceptance;


}

