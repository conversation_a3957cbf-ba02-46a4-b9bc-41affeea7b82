package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 签证单模板导出数据
 */
@Data
public class VisaExcelDto {

    private Long visaId;

    /** 签证单单号 */
    private String applyNo;

    /** 施工单位 */
    private String constructionUnit;

    /** 申请日期 */
    private Timestamp applyDate;

    /** 门店名称 */
    private String storeName;

    /** 项目名称 */
    private String projectName;

    /** 项目类型 */
    private String projectType;

    /** 请示内容 */
    private String content;

    /** 审批人角色 */
    private String  approveRoleName;

    /** 审批人 */
    private String  approveRole;

    /** 审批状态 */
    private String approveStatus;

    /** 审批结果 */
    private String approveResult;

    /** 审批时间 */
    private Timestamp approveEnd;

    /** 审批意见 */
    private String approveOption;

    /** 申请人姓名 */
    private String name;

    /** 区域经理 */
    private String areaManager;
}
