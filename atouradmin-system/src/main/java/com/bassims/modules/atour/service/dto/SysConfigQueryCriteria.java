/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.bassims.annotation.QueryPlus;
import lombok.Data;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-04-19
**/
@Data
public class SysConfigQueryCriteria{

    /** 精确 */
    @QueryPlus
    private Long configId;

    /** 模糊 */
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String configName;

    /** 模糊 */
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String configValue;

    /** 模糊 */
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String configRemark;

    /** 精确 */
    @QueryPlus
    private Boolean isDelete;
}