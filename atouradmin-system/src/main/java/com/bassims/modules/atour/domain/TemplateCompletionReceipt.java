/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-01-26
**/
@Data
@TableName(value="t_template_completion_receipt")
public class TemplateCompletionReceipt implements Serializable {

    @TableId(value = "receipt_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long receiptId;

    @TableField(value = "total_item")
    @ApiModelProperty(value = "总项(码值completion_total_items)")
    private String totalItem;

    @TableField(value = "serial_number")
    @ApiModelProperty(value = "序号")
    private String serialNumber;

    @TableField(value = "sub_item")
    @ApiModelProperty(value = "分项")
    private String subItem;

    @TableField(value = "project")
    @ApiModelProperty(value = "项目")
    private String project;

    @TableField(value = "content")
    @ApiModelProperty(value = "内容（码值completion_content）")
    private String content;

    @TableField(value = "sub_item_content")
    @ApiModelProperty(value = "分项")
    private String subItemContent;

    @TableField(value = "score")
    @ApiModelProperty(value = "分值")
    private String score;

    @TableField(value = "standard_score")
    @ApiModelProperty(value = "标准分值")
    private String standardScore;

    @TableField(value = "acceptance")
    @ApiModelProperty(value = "验收（码值 acceptance）")
    private String acceptance;

    @TableField(value = "get_score")
    @ApiModelProperty(value = "得分")
    private String getScore;

    @TableField(value = "classification")
    @ApiModelProperty(value = "分类（码值classification）")
    private String classification;

    @TableField(value = "qualification_rate")
    @ApiModelProperty(value = "合格率")
    private String qualificationRate;

    @TableField(value = "general_contracting_or_not")
    @ApiModelProperty(value = "是否总包（码值general_contracting_or_not）")
    private String generalContractingOrNot;

    @TableField(value = "general_contracting_standard_score")
    @ApiModelProperty(value = "总包标准分值")
    private String generalContractingStandardScore;

    @TableField(value = "overall_contracting_score")
    @ApiModelProperty(value = "总包得分")
    private String overallContractingScore;

    @TableField(value = "overall_contracting_score_rate")
    @ApiModelProperty(value = "总包得分率")
    private String overallContractingScoreRate;

    @TableField(value = "check_attachments")
    @ApiModelProperty(value = "现场照片")
    private String checkAttachments;

    @TableField(value = "standard_photos")
    @ApiModelProperty(value = "标准照片")
    private String standardPhotos;

    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField(value = "rectification_instructions")
    @ApiModelProperty(value = "整改说明")
    private String rectificationInstructions;

    @TableField(value = "rectification_attachments")
    @ApiModelProperty(value = "整改照片")
    private String rectificationAttachments;

    @TableField(value = "rectification_date")
    @ApiModelProperty(value = "整改日期")
    private Timestamp rectificationDate;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private String isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private String isDelete;

    @TableField(value = "example_diagram")
    @ApiModelProperty(value = "示例图")
    private String exampleDiagram;

    @TableField(value = "is_real_photos_taken")
    @ApiModelProperty(value = "是否是实拍照片（1是；0否）")
    private String isRealPhotosTaken;

    public void copy(TemplateCompletionReceipt source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}