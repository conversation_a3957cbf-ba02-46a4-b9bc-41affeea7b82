/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.requestParam.CreatePaymentApplicationRequest;
import com.bassims.modules.atour.service.PaymentApplicationService;
import com.bassims.modules.atour.service.dto.PaymentApplicationDto;
import com.bassims.modules.atour.service.dto.PaymentApplicationQueryCriteria;
import com.bassims.modules.atour.service.dto.RepayDto;
import com.bassims.modules.atour.service.dto.SummaryStatementDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-12-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "付款申请管理")
@RequestMapping("/api/paymentApplication")
public class PaymentApplicationController {

    private static final Logger logger = LoggerFactory.getLogger(PaymentApplicationController.class);

    private final PaymentApplicationService paymentApplicationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, PaymentApplicationQueryCriteria criteria) throws IOException {
        paymentApplicationService.download(paymentApplicationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<PaymentApplicationDto>>}
    */
    @GetMapping("/list")
    @Log("查询付款申请")
    @ApiOperation("查询付款申请")
    public ResponseEntity<Object> query(PaymentApplicationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(paymentApplicationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<PaymentApplicationDto>}
    */
    @GetMapping(value = "/getDetail")
    @Log("通过Id查询付款申请")
    @ApiOperation("查询付款申请")
    public ResponseEntity<Object> getDetail(CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.findById(createPaymentApplicationRequest.getProjectId(),createPaymentApplicationRequest.getContractType()),HttpStatus.OK);
    }

    @PostMapping("/create")
    @Log("创建付款申请")
    @ApiOperation("创建付款申请")
    public ResponseEntity<Object> create(@Validated @RequestBody CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.create(createPaymentApplicationRequest),HttpStatus.OK);
    }

    @GetMapping("/getData")
    @Log("根据项目id、合同类型获取系统带出参数")
    @ApiOperation("根据项目id、合同类型获取系统带出参数")
    public ResponseEntity<Object> getData(CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.getData(createPaymentApplicationRequest.getProjectId(),createPaymentApplicationRequest.getContractType()),HttpStatus.OK);
    }

    @GetMapping("/getDownStatement")
    @Log("根据项目id、合同类型获取首付对账单")
    @ApiOperation("根据项目id、合同类型获取首付对账单")
    public ResponseEntity<Object> getDownStatement(CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.getDownStatement(createPaymentApplicationRequest.getProjectId(),createPaymentApplicationRequest.getContractType()),HttpStatus.OK);
    }

    @GetMapping("/getConstructor")
    @Log("根据项目id获取项目施工方")
    @ApiOperation("根据项目id获取项目施工方")
    public ResponseEntity<Object> getConstructor(CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.getConstructor(createPaymentApplicationRequest.getProjectId()),HttpStatus.OK);
    }

    @PostMapping("/update")
    @Log("修改付款申请")
    @ApiOperation("修改付款申请")
    public ResponseEntity<Object> update(@Validated @RequestBody CreatePaymentApplicationRequest createPaymentApplicationRequest){
        return new ResponseEntity<>(paymentApplicationService.update(createPaymentApplicationRequest),HttpStatus.OK);
    }

    @PostMapping("/updateFirstPay")
    @Log("修改首付对账单")
    @ApiOperation("修改首付对账单")
    public ResponseEntity<Object> updateFirstPay(@Validated @RequestBody RepayDto repayDto){
        return new ResponseEntity<>(paymentApplicationService.updateOriginal(repayDto),HttpStatus.OK);
    }

    @PostMapping("/delete")
    @Log("删除付款申请")
    @ApiOperation("删除付款申请")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        paymentApplicationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List< SummaryStatementDto >>}
     */
    @GetMapping("/getSummaryStatementDto/{projectId}")
    @Log("获取项目汇总对账单")
    @ApiOperation("获取项目汇总对账单")
    public ResponseEntity<Object> getSummaryStatementDto(@PathVariable("projectId") Long projectId){
        return new ResponseEntity<>(paymentApplicationService.getSummaryStatementDto(projectId),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List< SummaryStatementDto >>}
     */
    @GetMapping("/getSummaryOrder")
    @Log("获取项目汇总对账单")
    @ApiOperation("获取项目汇总对账单")
    public ResponseEntity<Object> getSummaryOrder(Long projectId){
        return new ResponseEntity<>(paymentApplicationService.getSummaryOrderByProjectId(projectId),HttpStatus.OK);
    }

}