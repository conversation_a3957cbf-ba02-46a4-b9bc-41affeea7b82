/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.FinalPaymentApplication;
import com.bassims.modules.atour.service.FinalPaymentApplicationService;
import com.bassims.modules.atour.service.dto.FinalPaymentApplicationDto;
import com.bassims.modules.atour.service.dto.FinalPaymentApplicationQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-12-21
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "尾款申请管理")
@RequestMapping("/api/finalPaymentApplication")
public class FinalPaymentApplicationController {

    private static final Logger logger = LoggerFactory.getLogger(FinalPaymentApplicationController.class);

    private final FinalPaymentApplicationService finalPaymentApplicationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, FinalPaymentApplicationQueryCriteria criteria) throws IOException {
        finalPaymentApplicationService.download(finalPaymentApplicationService.queryAll(criteria), response);
    }

    /**
     * @real_return {@link ResponseEntity<List<FinalPaymentApplicationDto>>}
     */
    @GetMapping("/list")
    @Log("查询尾款申请")
    @ApiOperation("查询尾款申请")
    public ResponseEntity<Object> query(FinalPaymentApplicationQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(finalPaymentApplicationService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<FinalPaymentApplicationDto>}
     */
    @GetMapping(value = "/getDetail")
    @Log("通过Id查询尾款申请")
    @ApiOperation("查询尾款申请")
    public ResponseEntity<Object> getDetail(FinalPaymentApplication resources) {
        return new ResponseEntity<>(finalPaymentApplicationService.findById(resources.getProjectId(), resources.getContractType()), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增尾款申请")
    @ApiOperation("新增尾款申请")
    public ResponseEntity<Object> create(@Validated @RequestBody FinalPaymentApplication resources) {
        return new ResponseEntity<>(finalPaymentApplicationService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改尾款申请")
    @ApiOperation("修改尾款申请")
    public ResponseEntity<Object> update(@Validated @RequestBody FinalPaymentApplication resources) {
        finalPaymentApplicationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除尾款申请")
    @ApiOperation("删除尾款申请")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        finalPaymentApplicationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}