/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectUnauthorizedConstruction;
import com.bassims.modules.atour.service.ProjectUnauthorizedConstructionService;
import com.bassims.modules.atour.service.dto.ProjectUnauthorizedConstructionDto;
import com.bassims.modules.atour.service.dto.ProjectUnauthorizedConstructionQueryCriteria;
import com.bassims.modules.atour.service.dto.UnauthorizedConstructionDto;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-19
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "擅自施工与图纸不符部分管理")
@RequestMapping("/api/projectUnauthorizedConstruction")
public class ProjectUnauthorizedConstructionController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectUnauthorizedConstructionController.class);

    private final ProjectUnauthorizedConstructionService projectUnauthorizedConstructionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectUnauthorizedConstructionQueryCriteria criteria) throws IOException {
        projectUnauthorizedConstructionService.download(projectUnauthorizedConstructionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProjectUnauthorizedConstructionDto>>}
    */
    @GetMapping("/list")
    @Log("查询擅自施工与图纸不符部分")
    @ApiOperation("查询擅自施工与图纸不符部分")
    public ResponseEntity<Object> query(ProjectUnauthorizedConstructionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectUnauthorizedConstructionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProjectUnauthorizedConstructionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询擅自施工与图纸不符部分")
    @ApiOperation("查询擅自施工与图纸不符部分")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectUnauthorizedConstructionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增擅自施工与图纸不符部分")
    @ApiOperation("新增 擅自施工与图纸不符部分")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectUnauthorizedConstruction resources){
        return new ResponseEntity<>(projectUnauthorizedConstructionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改擅自施工与图纸不符部分")
    @ApiOperation("修改擅自施工与图纸不符部分")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectUnauthorizedConstruction resources){
        projectUnauthorizedConstructionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除擅自施工与图纸不符部分")
    @ApiOperation("删除擅自施工与图纸不符部分")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectUnauthorizedConstructionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }



    /**
     * @real_return {@link ResponseEntity<List<   UnauthorizedConstructionDto   >>}
     */
    @GetMapping("/queryByProjectId")
    @Log("通过项目ID查询擅自施工与图纸不符部分")
    @ApiOperation("通过项目ID查询擅自施工与图纸不符部分")
    public ResponseEntity<Object> queryByProjectId(ProjectUnauthorizedConstructionQueryCriteria criteria){
        return new ResponseEntity<>(projectUnauthorizedConstructionService.queryAll(criteria),HttpStatus.OK);
    }
}