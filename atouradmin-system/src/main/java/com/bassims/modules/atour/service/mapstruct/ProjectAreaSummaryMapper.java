package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectAreaSummary;
import com.bassims.modules.atour.service.dto.ProjectAreaSummaryDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 面积汇总映射
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectAreaSummaryMapper extends BaseMapper<ProjectAreaSummaryDto, ProjectAreaSummary> {
}
