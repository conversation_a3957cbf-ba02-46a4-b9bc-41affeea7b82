/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-21
**/
@Data
@TableName(value="s_expense_account")
public class ExpenseAccount implements Serializable {

    @TableId(value = "expense_account_id")
    @ApiModelProperty(value = "费用类型id")
    private Long expenseAccountId;

    @TableField(value = "level")
    @ApiModelProperty(value = "level")
    private Integer level;

    @TableField(value = "parentname")
    @ApiModelProperty(value = "parentname")
    private String parentname;

    @TableField(value = "itemname")
    @ApiModelProperty(value = "itemname")
    private String itemname;

    @TableField(value = "businessline")
    @ApiModelProperty(value = "businessline")
    private String businessline;

    @TableField(value = "parentcode")
    @ApiModelProperty(value = "parentcode")
    private String parentcode;

    @TableField(value = "importance")
    @ApiModelProperty(value = "importance")
    private String importance;

    @TableField(value = "businesslinecode")
    @ApiModelProperty(value = "businesslinecode")
    private String businesslinecode;

    @TableField(value = "budgetrealitycode")
    @ApiModelProperty(value = "budgetrealitycode")
    private String budgetrealitycode;

    @TableField(value = "budgetcode")
    @ApiModelProperty(value = "budgetcode")
    private String budgetcode;

    @TableField(value = "itemcode")
    @ApiModelProperty(value = "itemcode")
    private String itemcode;

    @TableField(value = "cashflow")
    @ApiModelProperty(value = "cashflow")
    private String cashflow;

    @TableField(value = "displayname")
    @ApiModelProperty(value = "displayname")
    private String displayname;

    @TableField(value = "budgetrealityname")
    @ApiModelProperty(value = "budgetrealityname")
    private String budgetrealityname;

    @TableField(value = "rangemarker")
    @ApiModelProperty(value = "rangemarker")
    private String rangemarker;

    @TableField(value = "budgetname")
    @ApiModelProperty(value = "budgetname")
    private String budgetname;

    @TableField(value = "accsubjcode")
    @ApiModelProperty(value = "accsubjcode")
    private String accsubjcode;

    @TableField(value = "accsubjname")
    @ApiModelProperty(value = "accsubjname")
    private String accsubjname;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ExpenseAccount source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}