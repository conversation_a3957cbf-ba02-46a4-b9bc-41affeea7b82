/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-01-26
 **/
@Data
public class TemplateCompletionReceiptDto implements Serializable {

    /**
     * 主键id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long receiptId;

    /**
     * 总项(码值completion_total_items)
     */
    private String totalItem;

    /**
     * 序号
     */
    private String serialNumber;

    /**
     * 分项
     */
    private String subItem;

    /**
     * 项目
     */
    private String project;

    /**
     * 内容（码值completion_content）
     */
    private String content;

    /**
     * 分项
     */
    private String subItemContent;

    /**
     * 分值
     */
    private String score;

    /**
     * 标准分值
     */
    private String standardScore;

    /**
     * 验收（码值acceptance）
     */
    private String acceptance;

    /**
     * 得分
     */
    private String getScore;

    /**
     * 分类（码值classification）
     */
    private String classification;

    /**
     * 合格率
     */
    private String qualificationRate;

    /**
     * 是否总包（码值general_contracting_or_not）
     */
    private String generalContractingOrNot;

    /**
     * 总包标准分值
     */
    private String generalContractingStandardScore;

    /**
     * 总包得分
     */
    private String overallContractingScore;

    /**
     * 总包得分率
     */
    private String overallContractingScoreRate;

    /**
     * 现场照片
     */
    private String checkAttachments;

    /**
     * 标准照片
     */
    private String standardPhotos;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 整改说明
     */
    private String rectificationInstructions;

    /**
     * 整改照片
     */
    private String rectificationAttachments;

    /**
     * 整改日期
     */
    private Timestamp rectificationDate;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否可用
     */
    private String isEnabled;

    /**
     * 是否删除
     */
    private String isDelete;

    /**
     * 示例图
     */
    private String exampleDiagram;

    /**
     * 是否是实拍照片（1是；0否）
     */
    private String isRealPhotosTaken;
}