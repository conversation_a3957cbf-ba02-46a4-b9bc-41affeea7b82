package com.bassims.modules.atour.domain.vo;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @title DownloadFileDto
 * @date 2023/4/4 14:39
 * @description TODO
 */
public class DownloadFileDto  implements Serializable {

    private static final long serialVersionUID = 2648469408255550980L;

    // 存放文件名
    private String fileName = "";

    // 存放文件字节流
    private byte[] byteDataArr = new byte[0];

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public byte[] getByteDataArr() {
        return byteDataArr;
    }

    public void setByteDataArr(byte[] byteDataArr) {
        this.byteDataArr = byteDataArr;
    }

    @Override
    public String toString() {
        return "DownloadFileDto{" +
                "fileName='" + fileName + '\'' +
                ", byteDataArr=" + Arrays.toString(byteDataArr) +
                '}';
    }
}
