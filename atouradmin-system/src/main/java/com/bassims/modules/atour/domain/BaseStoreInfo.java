/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-10
**/
@Data
@TableName(value="t_base_store_info")
public class BaseStoreInfo implements Serializable {

    @TableId(value = "store_id")
    @ApiModelProperty(value = "门店id主键")
    private Long storeId;

    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店no")
    private String storeNo;

    @TableField(value = "store_type")
    @ApiModelProperty(value = "门店类型")
    private String storeType;

    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @TableField(value = "region")
    @ApiModelProperty(value = "大区value")
    private String region;

    @TableField(value = "province")
    @ApiModelProperty(value = "省份id")
    private Long province;

    @TableField(value = "province_name")
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @TableField(value = "city")
    @ApiModelProperty(value = "城市id")
    private Long city;

    @TableField(value = "city_name")
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @TableField(value = "county")
    @ApiModelProperty(value = "区县id")
    private Long county;

    @TableField(value = "county_name")
    @ApiModelProperty(value = "区县名称")
    private String countyName;

    @TableField(value = "project_address")
    @ApiModelProperty(value = "详细地址（不包括省市县）")
    private String projectAddress;

    @TableField(value = "city_company")
    @ApiModelProperty(value = "城市公司value")
    private String cityCompany;

    @TableField(value = "is_active")
    @ApiModelProperty(value = "是否生效")
    private Boolean isActive;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "store_full_name")
    @ApiModelProperty(value = "门店全称")
    private String storeFullName;

    @TableField(value = "store_master_id")
    @ApiModelProperty(value = "门店主档id")
    private Long storeMasterId;

    public void copy(BaseStoreInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}