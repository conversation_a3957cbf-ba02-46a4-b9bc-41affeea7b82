package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectMarkReport;
import com.bassims.modules.atour.service.dto.ProjectMarkReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程运维项目评分报表
 *
 * <AUTHOR>
 * @date 2023/03/27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectMarkReportMapper extends BaseMapper<ProjectMarkReportDto, ProjectMarkReport> {

}
