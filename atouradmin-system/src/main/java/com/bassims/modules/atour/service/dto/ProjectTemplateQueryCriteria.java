/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.bassims.annotation.QueryPlus;
import lombok.Data;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-22
**/
@Data
public class ProjectTemplateQueryCriteria{

    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "templateCode")
    private String templateCode;
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE,propName = "nodeName" )
    private String nodeName;
    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "nodeIndex" )
    private double nodeIndex;
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE,propName = "remark" )
    private String remark;
    @QueryPlus(type = QueryPlus.Type.IS_NULL,propName = "parentId" )
    private Long parentIdForJava;

    @QueryPlus(type = QueryPlus.Type.IS_NULL,propName = "模版类型" )
    private String conditionTypeCode;

    @QueryPlus(type = QueryPlus.Type.IS_NULL,propName = "项目类型" )
    private String projectType;


    @QueryPlus(type = QueryPlus.Type.INNER_LIKE,propName = "类型" )
    private String templateType;

}