package com.bassims.modules.feishu.service;

import com.bassims.modules.atour.domain.ProjectNotice;
import com.bassims.modules.feishu.domain.AppletParameter;
import com.bassims.modules.feishu.domain.FeishuParameter;
import com.bassims.modules.feishu.domain.MessageParameter;
import com.bassims.modules.feishu.domain.TokenParameter;
import com.bassims.modules.security.service.dto.AuthUserDto;
import com.bassims.modules.system.domain.User;

public interface FeishuService {
    /**
     *获取当前程序的accessToken
     * @return
     */
    AppletParameter   getTenantAccessToken();

    /**
     * 获取当前用户的token
     * @param appletParameter
     * @param code
     * @return
     */
    TokenParameter getUserAccessData(AppletParameter appletParameter,String code);

    /**
     * 用token获取用户信息
     * @param tokenParameter
     * @return
     */
    FeishuParameter getUserInfoData(TokenParameter tokenParameter);

    /**
     * 整体处理飞书用户登录的信息
     * @param code
     * @return
     */
    AuthUserDto getUserInfoProcess(String code);

    /**
     * 获取用户的openid并更新
     * @param user
     * @return
     */
    User getUserOpenId(User user);

    /**
     * 发送消息
     * @param projectNotice
     * @return
     */
    MessageParameter sendMessage(ProjectNotice projectNotice);

    /**
     * 发送不带链接的消息
     * @param projectNotice
     * @return
     */
    MessageParameter sendMessageForDelete(ProjectNotice projectNotice);
}
