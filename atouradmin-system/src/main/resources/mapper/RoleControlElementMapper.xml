<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.RoleControlElementRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.RoleControlElement">
                    <id column="element_id" property="elementId" />
                    <result column="role_code" property="roleCode" />
                    <result column="finite_element" property="finiteElement" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            element_id,role_code,finite_element
        </sql>

    <select id="getPrepareSubmitted" resultType="java.lang.String">
        select GROUP_CONCAT(finite_element SEPARATOR ',')
        from t_role_control_element
        where role_code  in
        <foreach collection="roleCodes" item="roleCode" open="(" separator="," close=")">
            #{roleCode}
        </foreach>
    </select>

</mapper>
