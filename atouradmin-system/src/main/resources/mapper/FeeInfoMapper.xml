<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.FeeInfoRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.FeeInfo">
                    <id column="fee_id" property="feeId" />
                    <result column="project_id" property="projectId" />
                    <result column="unit_name" property="unitName" />
                    <result column="floor_area" property="floorArea" />
                    <result column="civil_engineer_cost" property="civilEngineerCost" />
                    <result column="mechatronics_cost" property="mechatronicsCost" />
                    <result column="water_cost" property="waterCost" />
                    <result column="subtotal" property="subtotal" />
                    <result column="create_by" property="createBy" />
                    <result column="create_time" property="createTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            fee_id,project_id,unit_name,floor_area,civil_engineer_cost,mechatronics_cost,water_cost,subtotal,create_by,create_time,update_by,update_time,is_enabled,is_delete
        </sql>

</mapper>
