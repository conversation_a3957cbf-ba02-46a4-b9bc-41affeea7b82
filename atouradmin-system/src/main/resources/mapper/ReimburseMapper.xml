<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ReimburseRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.Reimburse">
        <id column="reimburse_id" property="reimburseId"/>
        <result column="payment_application_id" property="paymentApplicationId"/>
        <result column="reimburse_total" property="reimburseTotal"/>
        <result column="tax_total" property="taxTotal"/>
        <result column="reimburse_to_tax_total" property="reimburseToTaxTotal"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        reimburse_id,payment_application_id,reimburse_total,tax_total,reimburse_to_tax_total,create_time,update_time,create_by,update_by,is_delete
    </sql>

</mapper>
