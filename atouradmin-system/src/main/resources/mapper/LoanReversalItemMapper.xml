<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.LoanReversalItemRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.LoanReversalItem">
        <id column="loan_reversal_item_id" property="loanReversalItemId"/>
        <result column="loan_reversal_id" property="loanReversalId"/>
        <result column="cost_third_code" property="costThirdCode"/>
        <result column="invoice_header" property="invoiceHeader"/>
        <result column="expense_dept" property="expenseDept"/>
        <result column="expense_dept_code" property="expenseDeptCode"/>
        <result column="loan_sn" property="loanSn"/>
        <result column="remain_reversal_amount" property="remainReversalAmount"/>
        <result column="reversal_amount" property="reversalAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        loan_reversal_item_id,loan_reversal_id,cost_third_code,invoice_header,expense_dept,expense_dept_code,loan_sn,remain_reversal_amount,reversal_amount,create_time,update_time,create_by,update_by,is_delete
    </sql>

</mapper>
