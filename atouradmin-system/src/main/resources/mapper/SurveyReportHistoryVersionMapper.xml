<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SurveyReportHistoryVersionRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SurveyReportHistoryVersion">
                    <id column="survey_report_version_id" property="surveyReportVersionId" />
                    <result column="approve_id" property="approveId" />
                    <result column="node_id" property="nodeId" />
                    <result column="submit_user" property="submitUser" />
                    <result column="project_id" property="projectId" />
                    <result column="submit_number" property="submitNumber" />
                    <result column="is_last" property="isLast" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            survey_report_version_id,approve_id,node_id,submit_user,project_id,submit_number,is_last,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

</mapper>
