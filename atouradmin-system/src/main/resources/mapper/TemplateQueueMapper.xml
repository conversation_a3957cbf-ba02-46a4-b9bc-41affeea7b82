<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateQueueRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateQueue">
        <id column="template_queue_id" property="templateQueueId"/>
        <result column="template_id" property="templateId"/>
        <result column="template_code" property="templateCode"/>
        <result column="parent_id" property="parentId"/>
        <result column="node_index" property="nodeIndex"/>
        <result column="node_level" property="nodeLevel"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="node_name" property="nodeName"/>
        <result column="node_code" property="nodeCode"/>


        <result column="one_template_id" property="oneTemplateId"/>
        <result column="two_template_id" property="twoTemplateId"/>
        <result column="stencil_level" property="stencilLevel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        template_queue_id,template_id,template_code,parent_id,node_index,node_level,is_enabled,is_delete,create_time,update_time,create_by,update_by,node_name,node_code,one_template_id,two_template_id,stencil_level
    </sql>

    <select id="getQueueTowList" resultType="com.bassims.modules.atour.domain.TemplateQueue">
        SELECT tq.*
        FROM t_template_queue tq
                 LEFT JOIN t_template_group tg ON tg.template_queue_id = tq.template_queue_id
        WHERE tq.template_code = #{templateCode}
          AND tq.node_level = 2;
    </select>

</mapper>
