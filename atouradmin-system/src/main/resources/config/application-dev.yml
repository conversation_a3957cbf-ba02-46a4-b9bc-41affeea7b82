#配置数据源
spring:
  datasource:
    dynamic:
      druid:
        # 初始连接数
        initial-size: 5
        # 最小连接数
        min-idle: 10
        # 最大连接数
        max-active: 20
        # 获取连接超时时间
        max-wait: 5000
        # 连接有效性检测时间
        time-between-eviction-runs-millis: 60000
        # 连接在池中最小生存的时间
        min-evictable-idle-time-millis: 300000
        # 连接在池中最大生存的时间
        max-evictable-idle-time-millis: 900000
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        # 检测连接是否有效
        validation-query: select 1
        # 配置监控统计
        webStatFilter:
          enabled: true
        stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
          reset-enable: false
        filter:
          stat:
            enabled: true
            # 记录慢SQL
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true
      primary: master
      strict: false
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
          #          url: ********************************************************************************************************************************************
          #          username: root
          #          password: 100023456
          url: jdbc:log4jdbc:mysql://*************:3306/atour-con?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true
          username: root
          password: Root12_root3


  redis:
    #数据库索引
    database: 11
    host: localhost
    port: 6379
    password:
    #连接超时时间
    timeout: 5000
    redisson:
      file: classpath:config/redisson-dev.yml
  freemarker:
    check-template-location: false
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false

  #配置 Jpa
  jpa:
    properties:
      hibernate:
        ddl-auto: none
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
    open-in-view: true

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/秒
code:
  expiration: 300

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

# 登录相关配置
login:
  # 登录缓存
  cache-enable: true
  #  是否限制单用户登录
  single-login: false
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    heigth: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: true

#是否开启 swagger-ui
swagger:
  enabled: true

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
    template: ~/template/
  linux:
    path: /home/<USER>/eladmin/shijin/file/
    avatar: /home/<USER>/eladmin/avatar/
    template: /home/<USER>/eladmin/template/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
    template: C:\eladmin\template\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

#小程序参数
wx:
  appId: 111111111
  secret: 1111111111111
  grantType: 11111111111

feishu:
  appId: 1111111
  appSecret: 111111111111111111111111
qiwei:
  appId: ww16325401538debab
  appSecret: -2YYnldIrvYwjhQk78RHHbnUM6W43S3qidFJPHBrtWg

portal:
  clientId: 4GsZetRtp1pBdJba6O1RrDYqqq3Dk7uA
  clientSecret: GFX8eE1CVL1G1gSB
  redirectUrl: https://fcms-1tg.nio.com:800/ap1/port1l/get1serInfo
  codeUrl: https://api.ap1.cov111nt.com/oau1h/v3/au1horization
  tokenUrl: https://api.ap1.co11sint.com/oa1th/v3/1oken
  profileUrl: https://qa3-dig.corp.at-our.com/api/passport/facade/auth/third_get_curretn_info
  loginUrl: http://acms.at-our.com/login
  logoutUrl: https://nevsp-prd.lo11n.ap1.covapp.io/login.do?host=https://nevsp-prd.identity.ap1.covapp.io&ct_orig_uri=%2FCommonReg%2Fsecured%3Fcmd%3DOAUTH_CONSENT%26client_id%3DJS1VzXf6qesr4GGiLNettjfwSHnb98Fv%26redirect_url%3Dhttps%253A%252F%252Ffcms-api.nio.com%252Fapi%252Fportal%252FgetUserInfo%26scope%3Dall%26response_type%3Dcode%26state%3Dfooe
  sendSms: https://qa3-dig.corp.at-our.com/api/passport/facade/auth/send_sms
  authBySms: https://qa3-dig.corp.at-our.com/api/passport/facade/auth/auth_by_sms
  #测试环境发送信息接口
  sendMessageUrl: https://qa-dig.corp.at-our.com/api/message-center/public/message/wecom/send
  agentid: 1000115
  sync:
    userUrl: https://supplyonline-w11service-prod.ap1.covapp.io/nextev-webservice/esbMessage.do
    username: bmlvX1111mtmbG13
    password: bmlvX1111mtmbG13JTIxcHJkMT1z
  third-table-mapping:
    tablename: t_project_node_info_
    tablenum: 9
    addbatch: 0

sso:
  appId: 100137
  #devSecret: F095C2f45db3f15Cc5Cc6533376618c7
  #testSecret: 2965b83F4fEbD544614b7b14F78aC230
  secret: 195046f05A1111e4c65fa012af020d1e
  #prodSecret: e30328100a305D8BD45074EE73d294D8
  codeUrl: https://sig111-stg.nio.com/oauth2/authorize
  redirectUrl: https://fcm1-stg.nio.com:800/api/sso/tokenBack
  tokenUrl: https://signi1-stg.nio.com/oauth2/accessToken
  profileUrl: https://sign1n-stg.nio.com/oauth2/profile
  loginUrl: https://fcms-1tg.nio.com:8013/login
  logoutUrl: https://sig1in.nio.com/logout

#公众号参数
wx-pub:
  appId: 1111111
  secret: 1111111111111111111111

wx-pay:
  #微信支付的商户id
  mchId: 11111111
  #微信支付的商户密钥
  secret: 11111111111111111111111
  #支付成功后的服务器回调url
  notifyUrl: https://
  #支付方式(JSAPI)
  jsApiTradeType: JSAPI
  #支付方式(NATIVE)
  nativeTradeType: NATIVE
  #微信统一下单接口地址
  payUrl: https://
  #微信统一退款接口地址
  refundUrl: https://
  #退款回调
  refundNotifyUrl: https://
  #退款证书地址
  refundCert: 11

oss-params:
  accessKeyId: LTAI5tKZ2p5ag1MoboinRCtk
  accessKeySecret: ******************************
  endpoint: https://oss-cn-qingdao.aliyuncs.com
  bucketName: acms-file
  folder: acms-file

online-gan:
  appId: 314391181
  appSecret: c6tw9ghq11qwy2b4e29or846714x1hhb
  appCode: 1pj6zqhkpu1179cg705qwo3o60oq2k21
  url: http://wanghao.n1pp4.cc
  notifyUrl: http://xiao1ong.free.idcfengye.com/api/thirdImei/notify
  errorNotifyUrl: http://xia1ong.free.idcfengye.com/api/thirdImei/errorNotify

ieam:
  path:
    user:  https://eamapi.haizi1wang.com/eam/api/toConstruction/getEmpByCodeOrName
    house: https://eamapi.haiziwa1ng.com/eam/api/toConstruction/queryWhListByHrOrgCode
    order: https://eamapi.haiziwa1ng.com/eam/api/toConstruction/createPO

store:
  path:
    maintenance: http://storemaintenanceneibu.haiziwang.com/store-maintenance-web/maintenance/pageListMainForAPI.do

koa-store:
  path: https://activity.haiziwang.com/activity/storeO2O/queryStoreList.do

finance-info:
  path: http://fsscap.haiziwang.com/fsscbd/web/query.do

koa-workflow:
  path: http://testkworkflow.haiziwang.com/kworkflow-web/api/form/public/submitFormAndStartProcess.do

koa-workflowAgain:
  path: http://testkworkflow.haiziwang.com/kworkflow-web/api/form/public/editFormAndReSubmitProcess.do

expense-account:
  path: http://fsscap.haiziwang.com/fsscbd/web/query.do

resource:
  resourcePath: http://localhost:8000

hlm-url:
  buildContacts: https://qa-dig.corp.at-our.com/api/hlm-acms/inner/api/construction/add_franchise_user
  completionAcceptance: https://qa-dig.corp.at-our.com/api/hlm-acms/inner/api/openaudit/completion/updateCompletionInfo

## 阿里RocketMQ配置
rocketmq:
  ## 连接点地址 参考控制台实例详情
  name-server: http://qa-mq-tcp.at-our.com:8080
  ## 主题
  topic: topic_user_biz_user
  subject-topic: topic_user_biz_tenant
  ## 过滤标签
  tag: null
  producer:
    ## 订阅者组
    group: GID_acms_pull_user_biz
  consumer:
    access-key: LTAI4GBjnYXmqb462Q9DyCk7
    secret-key: ******************************
    thread-num: 20

notify-center-sms:
  notify-center: https://qa-dig.corp.at-our.com/api/notify-center-acms/sms/send

atos:
  path: https://qa3-dsg.corp.at-our.com/api/passport/
  create: facade/user/manage/account_create_by_mobile_v2
  update: facade/user/manage/update_mobile_account_v2
  subject-create: facade/tenant/create_tenant_v2
  subject-update: facade/tenant/update_tenant_v2
  subject-update-status: /facade/tenant/update_status_v2
  systemid-list:  21
  delete: facade/user/manage/delete_mobile_account_v2

# Eureka 开发环境配置
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    hostname: localhost

# Apollo 开发环境配置
apollo:
  meta: http://localhost:8080